#!/usr/bin/env python3
"""
测试完整的ContourRetrieval初始化过程
"""

import torch
import torch.serialization
import pickle
import numpy as np
from pathlib import Path
from scipy.spatial import KDTree
import cv2

# 强制设置weights_only=False作为备用方案
_original_torch_load = torch.load
def _patched_torch_load(*args, **kwargs):
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)
torch.load = _patched_torch_load

# 添加安全全局变量
try:
    from ultralytics.nn.tasks import SegmentationModel
    torch.serialization.add_safe_globals([SegmentationModel])
except ImportError:
    pass

from ultralytics import YOLO

def test_complete_initialization():
    """测试完整的初始化过程"""
    print("=== 开始测试完整初始化过程 ===")
    
    # 1. 测试人体分割模型加载
    print("\n1. 测试人体分割模型加载...")
    human_model_path = Path('/Users/<USER>/Desktop/pose_matching_project/models/human_seg_model.pt')
    
    try:
        human_model = YOLO(str(human_model_path))
        print("✓ 人体分割模型加载成功")
    except Exception as e:
        print(f"✗ 人体分割模型加载失败: {e}")
        return False
    
    # 2. 测试动物数据库加载
    print("\n2. 测试动物数据库加载...")
    animal_db_path = Path('/Users/<USER>/Desktop/pose_matching_project/database/animal_contours.pkl')
    
    try:
        with open(animal_db_path, 'rb') as f:
            animal_db = pickle.load(f)
        print(f"✓ 动物数据库加载成功，包含 {len(animal_db['contours'])} 个轮廓")
    except Exception as e:
        print(f"✗ 动物数据库加载失败: {e}")
        return False
    
    # 3. 测试特征计算和索引构建
    print("\n3. 测试特征计算和索引构建...")
    try:
        print("正在计算特征...")
        features = []
        for i, contour in enumerate(animal_db['contours']):
            if i % 100 == 0:
                print(f"处理轮廓 {i}/{len(animal_db['contours'])}")
            
            # 计算形状特征
            moments = cv2.moments(contour)
            if moments['m00'] != 0:
                hu_moments = cv2.HuMoments(moments).flatten()
            else:
                hu_moments = np.zeros(7)
            
            # 安全的对数变换
            log_hu = np.zeros_like(hu_moments)
            for j, hu in enumerate(hu_moments):
                if abs(hu) > 1e-7:
                    log_hu[j] = -np.sign(hu) * np.log10(abs(hu))
                else:
                    log_hu[j] = 0
            
            features.append(log_hu)
        
        features = np.array(features)
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        print("正在构建KD-tree索引...")
        kdtree = KDTree(features)
        print("✓ 特征计算和索引构建成功")
        
    except Exception as e:
        print(f"✗ 特征计算和索引构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试动物识别模型加载（这可能是问题所在）
    print("\n4. 测试动物识别模型加载...")
    try:
        print("正在下载/加载 yolov8n.pt...")
        animal_recognition_model = YOLO('yolov8n.pt')
        print("✓ 动物识别模型加载成功")
    except Exception as e:
        print(f"✗ 动物识别模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        # 这里不返回False，因为原代码中这个是可选的
        print("注意：动物识别模型加载失败，但这是可选的")
    
    print("\n=== 所有测试完成 ===")
    return True

if __name__ == "__main__":
    test_complete_initialization() 