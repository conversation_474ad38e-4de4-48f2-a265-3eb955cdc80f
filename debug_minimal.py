#!/usr/bin/env python3
"""
最小化的调试脚本
"""

print("开始导入...")

# 按照原始代码的顺序逐步导入
import os
print("✓ os")

import cv2
print("✓ cv2")

import time
print("✓ time")

import uuid
print("✓ uuid")

import json
print("✓ json")

import pickle
print("✓ pickle")

import base64
print("✓ base64")

import pathlib
print("✓ pathlib")

import webbrowser
print("✓ webbrowser")

import traceback
print("✓ traceback")

import threading
print("✓ threading")

import http.server
print("✓ http.server")

import socketserver
print("✓ socketserver")

import urllib.parse
print("✓ urllib.parse")

# 全局PyTorch修复
import torch
print("✓ torch")

import torch.serialization
print("✓ torch.serialization")

# 强制设置weights_only=False作为备用方案
_original_torch_load = torch.load
def _patched_torch_load(*args, **kwargs):
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)
torch.load = _patched_torch_load
print("✓ torch补丁")

# 添加安全全局变量
try:
    from ultralytics.nn.tasks import SegmentationModel
    torch.serialization.add_safe_globals([SegmentationModel])
    print("✓ ultralytics安全全局变量")
except ImportError:
    print("✗ ultralytics导入失败")

import matplotlib.pyplot as plt
print("✓ matplotlib.pyplot")

import numpy as np
print("✓ numpy")

from pathlib import Path
print("✓ pathlib.Path")

from scipy.spatial import KDTree
print("✓ scipy.spatial.KDTree")

from ultralytics import YOLO
print("✓ ultralytics.YOLO")

# 添加OpenAI API支持
import requests
print("✓ requests")

from urllib.parse import parse_qs
print("✓ urllib.parse.parse_qs")

import random
print("✓ random")

import hashlib
print("✓ hashlib")

# 重新添加 Google Gemini API 导入
import google.generativeai as genai
print("✓ google.generativeai")

print("\n所有导入完成，开始测试实例化...")

# 尝试创建一个最小的ContourRetrieval类
class MinimalContourRetrieval:
    def __init__(self):
        print("开始最小初始化...")
        
        # 设置路径
        self.base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
        self.results_dir = Path('/Users/<USER>/Desktop/results')
        self.human_model_path = self.base_dir / 'models' / 'human_seg_model.pt'
        self.animal_db_path = self.base_dir / 'database' / 'animal_contours.pkl'
        
        print("路径设置完成")
        
        # 创建结果目录
        self.results_dir.mkdir(parents=True, exist_ok=True)
        print("结果目录创建完成")
        
        # 加载人体分割模型
        print("加载人体分割模型...")
        self.human_model = YOLO(str(self.human_model_path))
        print("人体分割模型加载完成")
        
        # 加载动物数据库
        print("加载动物数据库...")
        with open(self.animal_db_path, 'rb') as f:
            self.animal_db = pickle.load(f)
        print("动物数据库加载完成")
        
        # 计算特征
        print("计算特征...")
        self.features = []
        for contour in self.animal_db['contours']:
            moments = cv2.moments(contour)
            if moments['m00'] != 0:
                hu_moments = cv2.HuMoments(moments).flatten()
            else:
                hu_moments = np.zeros(7)
            self.features.append(hu_moments)
        
        self.features = np.array(self.features)
        self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)
        print("特征计算完成")
        
        # 构建索引
        print("构建KD-tree索引...")
        self.kdtree = KDTree(self.features)
        print("索引构建完成")
        
        # 加载动物识别模型
        print("加载动物识别模型...")
        try:
            self.animal_recognition_model = YOLO('yolov8n.pt')
            print("动物识别模型加载完成")
        except Exception as e:
            print(f"动物识别模型加载失败: {e}")
            self.animal_recognition_model = None
        
        print("最小初始化完成！")

if __name__ == "__main__":
    try:
        retrieval = MinimalContourRetrieval()
        print("✓ 成功创建MinimalContourRetrieval实例")
    except Exception as e:
        print(f"✗ 创建实例失败: {e}")
        import traceback
        traceback.print_exc() 