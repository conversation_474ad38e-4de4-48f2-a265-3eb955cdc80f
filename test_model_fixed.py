#!/usr/bin/env python3
import sys
import torch
from pathlib import Path

# 修复PyTorch 2.6+的兼容性问题
import torch.serialization
from ultralytics.nn.tasks import SegmentationModel
torch.serialization.add_safe_globals([SegmentationModel])

# 也可以通过强制设置weights_only=False
_original_torch_load = torch.load
def _patched_torch_load(*args, **kwargs):
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)
torch.load = _patched_torch_load

print("Python 版本:", sys.version)
print("PyTorch 版本:", torch.__version__)

# 设置模型路径
model_path = Path("models/human_seg_model.pt")
print(f"模型文件路径: {model_path}")
print(f"模型文件存在: {model_path.exists()}")

try:
    # 尝试加载 YOLO
    from ultralytics import YOLO
    print("YOLO 导入成功")
    
    # 尝试加载模型
    print("正在加载模型...")
    model = YOLO(str(model_path))
    print("模型加载成功!")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 