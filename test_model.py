#!/usr/bin/env python3
"""
简单的模型加载测试脚本
"""

import torch
import torch.serialization
from pathlib import Path

# 强制设置weights_only=False作为备用方案
_original_torch_load = torch.load
def _patched_torch_load(*args, **kwargs):
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)
torch.load = _patched_torch_load

# 添加安全全局变量
try:
    from ultralytics.nn.tasks import SegmentationModel
    torch.serialization.add_safe_globals([SegmentationModel])
except ImportError:
    pass

from ultralytics import YOLO

def test_model_loading():
    """测试模型加载"""
    print("开始测试模型加载...")
    
    model_path = Path('/Users/<USER>/Desktop/pose_matching_project/models/human_seg_model.pt')
    
    if not model_path.exists():
        print(f"错误：模型文件不存在: {model_path}")
        return False
    
    try:
        print(f"正在加载模型: {model_path}")
        model = YOLO(str(model_path))
        print("✓ 模型加载成功！")
        
        # 尝试获取模型信息
        print(f"模型类型: {type(model)}")
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model_loading() 